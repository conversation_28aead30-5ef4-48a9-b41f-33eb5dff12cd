package com.fs.fmcg.sdk.ai.cache;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataList;
import com.fmcg.framework.http.contract.paas.data.PaasDataQuery;
import com.fmcg.framework.http.contract.paas.data.PaasDataQuery.QueryDTO;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGetField;
import com.fs.fmcg.sdk.ai.adapter.IModelAdapter;
import com.fs.fmcg.sdk.ai.adapter.contract.GetModel;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import com.fs.fmcg.sdk.ai.error.SdkFailureException;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.notifier.support.NotifierClient;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Slf4j
@SuppressWarnings("Duplicates")
public class MetadataCache implements InitializingBean {

    @Resource
    private IModelAdapter modelAdapter;

    @Resource
    private PaasDataProxy paasDataProxy;


    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    private static Cache<String, Map<String, Object>> skuCache;
    private static Cache<String, Map<String, Object>> spuCache;
    private static Cache<String, List<String>> spuSkuRelationCache;
    private static Cache<String, GetModel.ModelDTO> modeCache;

    private static Cache<String, Map<String, Object>> objectCache;
    private static Cache<String, List<String>> objectRelationCache;
    private static Cache<String, Map<String, JSONObject>> fieldDescribeCache;
    private static Cache<String, Map<String, String>> unitRelationCache;

    private static Map<String, String> SPECIAL_MODEL_MAP = new HashMap<>();

    private static final int SUPER_USER_ID = -10000;
    private static final String SKU_OBJ = "ProductObj";
    private static final String SPU_OBJ = "SPUObj";
    private static final String SPU_SKU_RELATION = "SPU_SKU_RELATION";
    private static final String SPU_ID_FIELD_KEY = "spu_id";
    private static final String ID_FIELD = "_id";
    private static final String NOTIFIER_NOTICE_KEY = "FMCG_AI_SDK_CACHE_REFRESH";

    private static final Lock LOCK = new ReentrantLock();


    static {
        NotifierClient.register(NOTIFIER_NOTICE_KEY, message -> {
            if (!Strings.isNullOrEmpty(message.getContent())) {
                skuCache.invalidate(message.getContent());
                spuCache.invalidate(message.getContent());
                spuSkuRelationCache.invalidate(message.getContent());
                modeCache.invalidate(message.getContent());
                objectCache.invalidate(message.getContent());
                objectRelationCache.invalidate(message.getContent());
                fieldDescribeCache.invalidate(message.getContent());
                unitRelationCache.invalidate(message.getContent());
            }
            log.info("cache delete message:{}", message);
        });

        ConfigFactory.getConfig("fs-fmcg-tpm-config", config -> {
            String jsonStr = config.get("special_model_map");
            if (!Strings.isNullOrEmpty(jsonStr)) {
                SPECIAL_MODEL_MAP = JSON.parseObject(jsonStr, Map.class);
            }
        });
    }

    @Override
    public void afterPropertiesSet() {

        skuCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();

        spuCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();

        spuSkuRelationCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();

        objectCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();

        objectRelationCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();

        modeCache = CacheBuilder.newBuilder()
                .maximumSize(50)
                .expireAfterWrite(60, TimeUnit.MINUTES)
                .build();
        fieldDescribeCache = CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();
        unitRelationCache = CacheBuilder.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .build();

    }

    // public function

    /**
     * query spu object by spu id list, enable cache
     *
     * @param context call context
     * @param spuIds  spu id list
     * @return spu object list
     */
    public List<Map<String, Object>> querySpu(SdkContext context, Collection<String> spuIds) {
        return queryObject(context, spuCache, SPU_OBJ, spuIds);
    }

    public List<Map<String, Object>> querySku(SdkContext context, Collection<String> skuIds) {
        return queryObject(context, skuCache, SKU_OBJ, skuIds);
    }

    public List<Map<String, Object>> queryObjectData(SdkContext context, String apiName, Collection<String> dataIds) {
        return queryObject(context, objectCache, apiName, dataIds);
    }

    /**
     * @param context
     * @param relatedApiName           被关联对象apiName
     * @param relatedIds               被关联对象id
     * @param apiName                  本对象apiName
     * @param relationshipFieldApiName 本对象查找关联的字段apiName
     * @return
     */
    public List<Map<String, Object>> queryRelatedObject(SdkContext context, String relatedApiName, Collection<String> relatedIds, String apiName, String relationshipFieldApiName) {
        List<Map<String, Object>> rst = new ArrayList<>();
        Set<String> missCacheIds = new HashSet<>();
        for (String id : relatedIds) {
            String key = buildCacheKey(context, relatedApiName, id);
            List<String> ids = objectRelationCache.getIfPresent(key);
            if (ids != null) {
                rst.addAll(queryData(context, apiName, ids));
            } else {
                missCacheIds.add(id);
            }
        }
        if (!missCacheIds.isEmpty()) {
            PaasDataQuery.FilterDTO relatedIdFilter = new PaasDataQuery.FilterDTO();
            relatedIdFilter.setFieldName(relationshipFieldApiName);
            relatedIdFilter.setOperator("IN");
            relatedIdFilter.setFieldValues(missCacheIds);

            PaasDataQuery.FilterDTO relatedIdNotNullFilter = new PaasDataQuery.FilterDTO();
            relatedIdNotNullFilter.setFieldName(relationshipFieldApiName);
            relatedIdNotNullFilter.setOperator("ISN");
            relatedIdNotNullFilter.setFieldValues(Lists.newArrayList(""));

            List<JSONObject> detailList = queryData(context, apiName, relatedIdFilter, relatedIdNotNullFilter);
            Map<String, List<String>> relation = new HashMap<>();
            for (JSONObject object : detailList) {
                String relatedId = object.getString(relationshipFieldApiName);
                String id = object.getString("_id");
                String key = buildCacheKey(context, relationshipFieldApiName, relatedId);
                if (relation.containsKey(key)) {
                    relation.get(key).add(id);
                } else {
                    relation.put(key, Lists.newArrayList(id));
                }
            }
            objectRelationCache.putAll(relation);
            rst.addAll(detailList);
        }
        return rst;
    }

    public GetModel.ModelDTO getModel(SdkContext context, String modelId) {
        String storeKey = SPECIAL_MODEL_MAP.containsKey(modelId) ? SPECIAL_MODEL_MAP.get(modelId) : context.getTenantId() + "." + modelId;
        GetModel.ModelDTO model = modeCache.getIfPresent(storeKey);
        if (model != null) {
            log.info("[AI_SDK] load model from cache success.");
            return model;
        }
        LOCK.lock();
        try {
            model = modeCache.getIfPresent(storeKey);
            if (model != null) {
                return model;
            }
            context = freshContext(context, modelId);
            GetModel.Result modelResult = loadModel(context, modelId);
            modeCache.put(storeKey, modelResult.getData().getModel());
            return modelResult.getData().getModel();
        } finally {
            LOCK.unlock();
        }
    }

    private SdkContext freshContext(SdkContext context, String modelId) {
        if (SPECIAL_MODEL_MAP.containsKey(modelId)) {
            String tenantId = SPECIAL_MODEL_MAP.get(modelId).split("\\.")[0];
            return SdkContext.createInstance(Integer.parseInt(tenantId), null, -10000, context.getTraceId());
        }
        return context;
    }


    private GetModel.Result loadModel(SdkContext context, String modelId) {
        if (GrayRelease.isAllow("fmcg", "YQSL_REAL_TIME_AI_MODEL", context.getTenantId())) {
            GetModel.Arg modelArg = new GetModel.Arg();
            modelArg.setModelId(modelId);
            modelArg.setIncludeObjectMap(false);

            GetModel.Result modelResult = modelAdapter.get(context.getTenantId(), context.getCurrentEmployeeId(), context.getTraceId(), modelArg);

            if (!modelResult.isSuccess()) {
                throw new SdkFailureException(modelResult.getCode(), modelResult.getMessage());
            }

            List<JSONObject> skuList = queryYQSKU(context);
            modelResult.getData().getModel().setObjectMapList(Lists.newArrayList());

            for (JSONObject sku : skuList) {
                GetModel.ObjectMapDTO datum = new GetModel.ObjectMapDTO();
                datum.setObjectId(sku.getString("_id"));
                datum.setKey(sku.getString("product_code"));

                datum.setApiName(SKU_OBJ);
                datum.setUnit("1");
                datum.setAppId("CRM");
                datum.setColor("123456");
                datum.setThreshold(0D);

                modelResult.getData().getModel().getObjectMapList().add(datum);
            }

            return modelResult;
        } else {
            GetModel.Arg modelArg = new GetModel.Arg();
            modelArg.setModelId(modelId);
            modelArg.setIncludeObjectMap(true);

            GetModel.Result modelResult = modelAdapter.get(context.getTenantId(), context.getCurrentEmployeeId(), context.getTraceId(), modelArg);

            if (!modelResult.isSuccess()) {
                throw new SdkFailureException(modelResult.getCode(), modelResult.getMessage());
            }
            return modelResult;
        }
    }

    private List<JSONObject> queryYQSKU(SdkContext context) {
        if (GrayRelease.isAllow("fmcg", "yqsl_object_map_query_sku_with_fields", context.getTenantId())) {
            PaasDataQueryWithFields.FilterDTO productCodeIsNotEmptyFilter = new PaasDataQueryWithFields.FilterDTO();
            productCodeIsNotEmptyFilter.setFieldName("product_code");
            productCodeIsNotEmptyFilter.setOperator("NEQ");
            productCodeIsNotEmptyFilter.setFieldValues(Lists.newArrayList(""));

            PaasDataQueryWithFields.FilterDTO productCodeIsNotNullFilter = new PaasDataQueryWithFields.FilterDTO();
            productCodeIsNotNullFilter.setFieldName("product_code");
            productCodeIsNotNullFilter.setOperator("ISN");
            productCodeIsNotNullFilter.setFieldValues(Lists.newArrayList());
            return queryDataByFields(context, SKU_OBJ, Lists.newArrayList(productCodeIsNotEmptyFilter, productCodeIsNotNullFilter), Lists.newArrayList("_id", "product_code"));
        } else {
            PaasDataQuery.FilterDTO productCodeIsNotEmptyFilter = new PaasDataQuery.FilterDTO();
            productCodeIsNotEmptyFilter.setFieldName("product_code");
            productCodeIsNotEmptyFilter.setOperator("NEQ");
            productCodeIsNotEmptyFilter.setFieldValues(Lists.newArrayList(""));

            PaasDataQuery.FilterDTO productCodeIsNotNullFilter = new PaasDataQuery.FilterDTO();
            productCodeIsNotNullFilter.setFieldName("product_code");
            productCodeIsNotNullFilter.setOperator("ISN");
            productCodeIsNotNullFilter.setFieldValues(Lists.newArrayList());
            return queryData(context, SKU_OBJ, productCodeIsNotEmptyFilter, productCodeIsNotNullFilter);
        }
    }

    public List<Map<String, Object>> queryRelatedSku(SdkContext context, Collection<String> spuIds) {
        List<Map<String, Object>> rst = new ArrayList<>();
        Set<String> missCacheIds = new HashSet<>();
        for (String id : spuIds) {
            String key = buildCacheKey(context, SPU_SKU_RELATION, id);
            List<String> skuIds = spuSkuRelationCache.getIfPresent(key);
            if (skuIds != null) {
                rst.addAll(querySku(context, skuIds));
            } else {
                missCacheIds.add(id);
            }
        }
        if (!missCacheIds.isEmpty()) {

            PaasDataQuery.FilterDTO spuIdFilter = new PaasDataQuery.FilterDTO();
            spuIdFilter.setFieldName(SPU_ID_FIELD_KEY);
            spuIdFilter.setOperator("IN");
            spuIdFilter.setFieldValues(missCacheIds);

            PaasDataQuery.FilterDTO spuIdNotNullFilter = new PaasDataQuery.FilterDTO();
            spuIdNotNullFilter.setFieldName(SPU_ID_FIELD_KEY);
            spuIdNotNullFilter.setOperator("ISN");
            spuIdNotNullFilter.setFieldValues(Lists.newArrayList(""));

            List<JSONObject> skuList = queryData(context, SKU_OBJ, spuIdFilter, spuIdNotNullFilter);

            Map<String, List<String>> relation = new HashMap<>();
            for (JSONObject object : skuList) {
                String spuId = object.getString(SPU_ID_FIELD_KEY);
                String id = object.getString("_id");
                String key = buildCacheKey(context, SPU_SKU_RELATION, spuId);
                if (relation.containsKey(key)) {
                    relation.get(key).add(id);
                } else {
                    relation.put(key, Lists.newArrayList(id));
                }
            }

            spuSkuRelationCache.putAll(relation);
            rst.addAll(skuList);
        }
        return rst;
    }

    // private methods

    private String buildCacheKey(SdkContext context, String prefix, String id) {
        return String.format("FMCG.SDK.%s.%s.%s", context.getTenantId(), prefix, id);
    }

    private List<Map<String, Object>> queryObject(SdkContext context, Cache<String, Map<String, Object>> cache, String apiName, Collection<String> ids) {
        List<Map<String, Object>> rst = new ArrayList<>();

        // id list of missing cache
        Set<String> missCacheIds = new HashSet<>();

        for (String id : ids) {
            // init identity cache key
            String key = buildCacheKey(context, apiName, id);

            Map<String, Object> cacheItem = cache.getIfPresent(key);
            if (cacheItem != null) {
                rst.add(cacheItem);
            } else {
                missCacheIds.add(id);
            }
        }
        if (!missCacheIds.isEmpty()) {
            List<JSONObject> dataList = queryData(context, apiName, missCacheIds);
            for (JSONObject data : dataList) {
                String key = buildCacheKey(context, apiName, data.getString("_id"));
                cache.put(key, data);
                rst.add(data);
            }
        }
        return rst;
    }

    public List<JSONObject> queryData(SdkContext context, String apiName, Collection<String> ids) {
        PaasDataQuery.FilterDTO idFilter = new PaasDataQuery.FilterDTO();
        idFilter.setFieldName(ID_FIELD);
        idFilter.setOperator("IN");
        idFilter.setFieldValues(Lists.newArrayList(ids));

        return queryData(context, apiName, idFilter);
    }


    public List<JSONObject> queryDataByFields(SdkContext context, String apiName, List<PaasDataQueryWithFields.FilterDTO> filters, List<String> fields) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        arg.setFieldList(fields);

        final int limit = 1000;

        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO();

        query.setLimit(limit);
        query.setOffset(0);

        query.setNeedReturnQuote(true);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnCountNum(false);
        query.setFilters(Lists.newArrayList(filters));

        PaasDataQueryWithFields.OrderDTO idOrder = new PaasDataQueryWithFields.OrderDTO();
        idOrder.setFieldName("_id");
        idOrder.setIsAsc(true);

        query.setOrders(Lists.newArrayList(idOrder));

        arg.setQueryString(JSON.toJSONString(query));

        List<JSONObject> data = Lists.newArrayList();

        int loop = 0;
        while (loop <= 100) {
            PaasDataQueryWithFields.Result result = paasDataProxy.queryWithFields(context.getTenantId(), SUPER_USER_ID, apiName, arg);
            if (result.getErrCode() != 0) {
                throw new SdkFailureException(result.getErrCode(), result.getErrMessage());
            }
            data.addAll(result.getResult().getQueryResult().getDataList());

            if (result.getResult().getQueryResult().getDataList().size() < limit) {
                break;
            }

            query.setOffset(query.getOffset() + limit);
            arg.setQueryString(JSON.toJSONString(query));
            loop++;
        }

        return data;
    }

    public List<JSONObject> queryData(SdkContext context, String apiName, PaasDataQuery.FilterDTO... filters) {
        PaasDataQuery.Arg arg = new PaasDataQuery.Arg();
        arg.setObjectApiName(apiName);

        final int limit = 200;

        QueryDTO query = new QueryDTO();

        query.setLimit(limit);
        query.setOffset(0);

        query.setNeedReturnQuote(true);
        query.setFindExplicitTotalNum(false);
        query.setNeedReturnCountNum(false);
        query.setFillExtendInfo(false);
        query.setFilters(Lists.newArrayList(filters));

        PaasDataQuery.OrderDTO idOrder = new PaasDataQuery.OrderDTO();
        idOrder.setFieldName("_id");
        idOrder.setIsAsc(true);

        query.setOrders(Lists.newArrayList(idOrder));

        arg.setQuery(query);

        arg.setIncludeDescribe(false);
        arg.setIncludeLayout(false);
        arg.setIncludeButtonInfo(false);

        List<JSONObject> data = Lists.newArrayList();

        int loop = 0;
        while (loop <= 100) {
            PaasDataQuery.Result result = paasDataProxy.query(context.getTenantId(), SUPER_USER_ID, true, arg);
            if (result.getErrCode() != 0) {
                throw new SdkFailureException(result.getErrCode(), result.getErrMessage());
            }
            data.addAll(result.getResult().getDataList());

            if (result.getResult().getDataList().size() < limit) {
                break;
            }

            query.setOffset(query.getOffset() + limit);
            loop++;
        }

        return data;
    }

    public List<JSONObject> queryCommonObjectWithFields(Integer tenantId, String apiName, List<PaasDataQueryWithFields.FilterDTO> filters, List<String> fields) {
        PaasDataQueryWithFields.Arg arg = new PaasDataQueryWithFields.Arg();
        PaasDataQueryWithFields.OrderDTO orderDTO = new PaasDataQueryWithFields.OrderDTO();
        orderDTO.setFieldName("_id");
        orderDTO.setIsAsc(false);

        PaasDataQueryWithFields.QueryDTO query = new PaasDataQueryWithFields.QueryDTO.Builder()
                .offset(0)
                .limit(2000)
                .searchSource("es")
                .needReturnQuote(false)
                .needReturnCountNum(false)
                .appendOrder(orderDTO)
                .appendFilters(filters)
                .build();
        arg.setQueryString(JSON.toJSONString(query));
        arg.setFieldList(fields);
        PaasDataQueryWithFields.Result data = paasDataProxy.queryWithFields(tenantId, -10000, apiName, arg);
        if (data.getErrCode() == 0) {
            return data.getResult().getQueryResult().getDataList();
        } else {
            log.info("query err:{}.", data);
            throw new SdkFailureException(data.getErrCode(), "ai query fail.");
        }
    }

    public Map<String, String> queryBigAndSmallUnitIds(SdkContext context, List<String> queryKeys) {
        Map<String, String> unitMap = new HashMap<>();
        List<String> leftQueryKeys = new ArrayList<>(queryKeys);
        String cacheKey = String.valueOf(context.getTenantId());
        Map<String, String> cacheUnitMap = unitRelationCache.getIfPresent(cacheKey);
        for (String unitKey : queryKeys) {
            if (!MapUtils.isNullOrEmpty(cacheUnitMap) && cacheUnitMap.containsKey(unitKey)) {
                unitMap.put(unitKey, cacheUnitMap.get(unitKey));
            } else {
                leftQueryKeys.add(unitKey);
            }
        }
        if (!CollectionUtils.isEmpty(leftQueryKeys)) {
            Map<String, String> additionUnitMap = loadUnitRelationCache(context, leftQueryKeys);
            unitMap.putAll(additionUnitMap);
        }

        return unitMap;
    }

    private Map<String, String> loadUnitRelationCache(SdkContext context, List<String> queryKeys) {
        String cacheKey = String.valueOf(context.getTenantId());
        Map<String, String> addtionMap = new HashMap<>();
        synchronized (cacheKey.intern()) {
            //MultiUnitRelatedObj
            Map<String, String> cacheUnitMap = unitRelationCache.getIfPresent(cacheKey);
            List<String> skuIds = queryKeys.stream().map(key -> key.split(":")[0]).collect(Collectors.toList());
            PaasDataQueryWithFields.FilterDTO skuIdFilter = new PaasDataQueryWithFields.FilterDTO("product_id", "IN", skuIds);
            PaasDataQueryWithFields.FilterDTO unitTypeFilter = new PaasDataQueryWithFields.FilterDTO("unit_type", "IN", Lists.newArrayList("large", "small"));
            List<JSONObject> multiUnitRelationList = queryCommonObjectWithFields(context.getTenantId(), "MultiUnitRelatedObj", Lists.newArrayList(skuIdFilter, unitTypeFilter), Lists.newArrayList("_id", "product_id", "unit_id", "unit_type"));
            multiUnitRelationList.forEach(relation -> {
                String productId = relation.getString("product_id");
                String unitId = relation.getString("unit_id");
                String unitType = relation.getString("unit_type");
                addtionMap.put(productId + ":" + unitType, unitId);
            });
            if (MapUtils.isNullOrEmpty(cacheUnitMap)) {
                unitRelationCache.put(cacheKey, addtionMap);
            } else {
                cacheUnitMap.putAll(addtionMap);
            }
        }
        return addtionMap;
    }

    public List<JSONObject> list(Integer tenantId, String apiName, List<String> ids) {
        PaasDataQuery.QueryDTO queryDTO = new PaasDataQuery.QueryDTO();
        queryDTO.setLimit(1);
        queryDTO.setOffset(0);

        PaasDataQuery.FilterDTO filterDTO = new PaasDataQuery.FilterDTO();
        filterDTO.setFieldName("_id");
        filterDTO.setOperator("IN");
        filterDTO.setFieldValues(ids);
        queryDTO.setFilters(Lists.newArrayList(filterDTO));

        PaasDataList.Arg arg = new PaasDataList.Arg();
        arg.setQueryInfo(JSON.toJSONString(queryDTO));
        arg.setObjectApiName(apiName);
        PaasDataQuery.Result paasResult = paasDataProxy.list(tenantId, -10000, apiName, arg);
        if (paasResult.getErrCode() != 0) {
            throw new SdkFailureException(paasResult.getErrCode(), paasResult.getErrMessage());
        }

        return paasResult.getResult().getDataList();

    }

    public JSONObject getFieldDescribe(Integer tenantId, String describeApiName, String fieldName) {
        String key = String.format("%d.%s", tenantId, describeApiName);
        Map<String, JSONObject> describeFieldMap = fieldDescribeCache.getIfPresent(key);

        if (describeFieldMap == null) {
            synchronized (key.intern()) {
                describeFieldMap = fieldDescribeCache.getIfPresent(key);
                if (describeFieldMap == null) {
                    describeFieldMap = new HashMap<>();
                    PaasDescribeGetField.Arg arg = new PaasDescribeGetField.Arg();
                    arg.setDescribeApiName(describeApiName);
                    arg.setFieldApiName(fieldName);
                    PaasDescribeGetField.Result result = paasDescribeProxy.findCustomFieldDescribe(tenantId, -10000, arg);
                    if (result.getErrCode() != 0) {
                        throw new SdkFailureException(result.getErrCode(), result.getErrMessage());
                    }
                    describeFieldMap.put(fieldName, result.getResult().getField());
                }
                fieldDescribeCache.put(key, describeFieldMap);
            }
        }
        return describeFieldMap.get(fieldName);
    }
}
