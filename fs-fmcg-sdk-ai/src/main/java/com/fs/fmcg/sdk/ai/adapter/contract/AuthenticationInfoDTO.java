package com.fs.fmcg.sdk.ai.adapter.contract;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class AuthenticationInfoDTO {

    private Integer tenantId;

    private Integer userId;

    private String source;

    private String type;

    private String token;


    public <T> T getAuth() {
        if ("token".equals(type)) {
            return (T)token;
        }
        return null;
    }

}
