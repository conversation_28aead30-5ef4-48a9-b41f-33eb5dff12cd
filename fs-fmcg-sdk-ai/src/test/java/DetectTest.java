import com.alibaba.fastjson.JSON;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.fs.fmcg.sdk.ai.DetectClient;
import com.fs.fmcg.sdk.ai.adapter.contract.CommonDetect;
import com.fs.fmcg.sdk.ai.adapter.contract.GetModel;
import com.fs.fmcg.sdk.ai.cache.MetadataCache;
import com.fs.fmcg.sdk.ai.contract.Detect;
import com.fs.fmcg.sdk.ai.contract.ObjectDTO;
import com.fs.fmcg.sdk.ai.contract.PureDetect;
import com.fs.fmcg.sdk.ai.contract.SdkContext;
import com.fs.fmcg.sdk.ai.plat.DetectCenter;
import com.fs.fmcg.sdk.ai.plat.TokenFactory;
import com.fs.fmcg.sdk.ai.trace.AITraceContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DetectTest extends TestBase {

    @Resource
    private DetectClient detectClient;
    @Resource
    private MetadataCache metadataCache;
    @Resource
    private TokenFactory tokenFactory;



    @Resource
    private DetectCenter detectCenter;

    @Test
    public void simpleDetectTest() {

        SdkContext context = SdkContext.createInstance(84931, "84931", 1001, "STONE");
        Detect.Arg arg = new Detect.Arg();

        arg.setModelId("6788d5b3b6ab2619671a9a70");
        arg.setPath("N_202501_20_e3ac71bac5f14004b5582fa143b52b04");
        arg.setScene("new_number_form");

        Detect.Result result = detectClient.detect(context, arg);

        assert result != null;
    }

    @Test
    public void detectTest() throws IOException {

//        File file = new File("/Users/<USER>/Documents/classify_test/recapture/recapture_14.jpg");
        File file = new File("/Users/<USER>/Downloads/zxgt.jpg");

        byte[] bytes = new byte[(int) file.length()];
        FileInputStream fis = new FileInputStream(file);
        int flag = fis.read(bytes);


        SdkContext context = SdkContext.createInstance(
                82325,
                "82325",
                1001,
                "STONE");

        Detect.Arg arg = new Detect.Arg();
        //66eaa408ac9063096be9f3de 图匠89273  6788d5b3b6ab2619671a9a70 朗境84931
        arg.setModelId("61b6b645cb69647054310f88");
        //TN_179515990c9345499fcdb568519f938c   TN_2601bd54c7644c8aa37cb6b8a47f36e5
        //TN_30641221479941aba27d0009d1f250d8   TN_ab103a8786514e7f898cf881176c8a9d TN_c4ba6e8e82d64c02b7d02384df34242f
        arg.setPath("TN_df5e8ad51bdc4ac0bd496c0e88544c77");
        arg.setScene("new_number_form");
        arg.setImage(bytes);
        arg.setMustRecord(false);
        arg.setExtraData("{\"enable_recapture_classify\" : false, \"enable_scene_classify\" : true, \"account_id\" : \"65a4a5dba72d5f0001cb52c1\",\"scene_type\" : \"hj\", \"openSkuUnit\" : true,\"enable_similar_image_detect\":1}");

        Detect.Result result = detectClient.detect(context, arg);
        System.out.println("-------------");
        System.out.println(JSON.toJSONString(result));
        System.out.println("-------------");
        //drawBox(bytes, result.getData().getObjectList(), "test");
        assert result != null;
    }

    @Test
    public void testDetectCenter() throws IOException {

        File file = new File("/Users/<USER>/Downloads/Photo_20230504_163803_328.jpg");
        byte[] bytes = new byte[(int) file.length()];
        FileInputStream fis = new FileInputStream(file);
        int flag = fis.read(bytes);
        SdkContext context = SdkContext.createInstance(
                78582,
                "78582",
                1000,
                "STONE");
        AITraceContext aiTraceContext = AITraceContext.get();
        aiTraceContext.setNPath("123");
        GetModel.ModelDTO model = metadataCache.getModel(context, "61a4a8c1cb69647054e86072");
        System.out.println(JSON.toJSONString(detectCenter.detect(model, bytes, "", null)));
    }


    @Test
    public void detectWithOutSpuTest() throws IOException {
        long start = System.currentTimeMillis();

        SdkContext context = SdkContext.createInstance(
                84931,
                "84931",
                1000,
                "STONE");

        PureDetect.Arg arg = new PureDetect.Arg();
        arg.setModelId("639c2f1fd3245cdbd12dc028");
        arg.setPath("TN_cb73fb616fe843f0b926a814b0f80ba2_tmb");
        PureDetect.Result result = detectClient.detectWithModel(context, arg);

        long stamp = System.currentTimeMillis() - start;
        System.out.println(stamp);
        System.out.println(JSON.toJSONString(result));
        assert result != null;
    }


    public void drawBox(byte[] imageStream, List<ObjectDTO> boxes, String name) throws IOException {
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageStream));
        Graphics2D graphics = (Graphics2D) image.getGraphics();
        Map<String, Color> colorMap = new HashMap<>();

        int index = 0;
        for (ObjectDTO box : boxes) {
            if (!colorMap.containsKey(box.getDataId())) {
                colorMap.put(box.getDataId(), getColor(box.getColor()));
                index = index + 1;
            }
            graphics.setColor(colorMap.get(box.getDataId()));
            graphics.setStroke(new BasicStroke(10.0f));

            int left = (int) box.getPosition().getX();
            int top = (int) box.getPosition().getY();
            int right = (int) (box.getPosition().getX() + box.getPosition().getW());
            int bottom = (int) (box.getPosition().getY() + box.getPosition().getH());

            graphics.drawRoundRect(left, top, right - left, bottom - top, 50, 50);
            double size = (right - left) / 8.0;
            Font font = new Font("微软雅黑", Font.BOLD, (int) size);
            graphics.setFont(font);
            graphics.drawString(box.getApiName(), left + 20, top - 10);
        }

        StoneFileUploadRequest uploadRequest = new StoneFileUploadRequest();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(image, "jpg", new File(String.format("/Users/<USER>/Downloads/rst_%s.jpg", name)));
    }

    @Test
    public void testCommonDetect() {
        SdkContext context = SdkContext.createInstance(
                84931,
                "84931",
                1000,
                "STONE");
        CommonDetect.Result result = detectClient.commonDetect(context, "65e1849534c9dc46a1fdeb58", "TN_f146f6bfdb794710addbfc47305fe7ad_tmb");
        System.out.println(JSON.toJSONString(result.getResults()));
    }

    private Color getColor(String color) {
        return new Color(Integer.parseInt(color.substring(0, 2), 16), Integer.parseInt(color.substring(2, 4), 16), Integer.parseInt(color.substring(4, 6), 16));
    }

    @Test
    public void testApplication() {

        System.out.println("+++++++++++:" + tokenFactory.getToken(83921, "BAIDU_LDB", true));
    }

    @Test
    public void testForceToken() {
        tokenFactory.refreshToken("TU_JIANG_PROD");
    }
}
